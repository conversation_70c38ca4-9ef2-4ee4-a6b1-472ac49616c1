from factor_evaluation.data_service import DataService
from vnpy_backtester.engines.engine import BacktestingEngine
from vnpy_backtester.engines.portfolio_engine import PortfolioEngine
from vnpy_backtester.strategies.ma_cross_trailing_stop_strategy import MACrossTrailingStopStrategy
from vnpy_backtester.utils.chart_engine import PlotlyChartEngine
from vnpy_backtester.objects.object import BarData
from vnpy_backtester.utils.constant import Exchange

import pandas as pd
from datetime import datetime


def run_ma_cross_trailing_stop_portfolio_backtest(

    dfs_dict,  # 字典，键为品种名称，值为对应的DataFrame
    symbol_params=None,  # 字典，键为品种名称，值为该品种的参数字典
    fast_window=10,
    slow_window=20,
    position_size=10,
    rate=0.0003,
    slippage=0.001,
    capital=50000,
    plot_show=True,
    plot_save=True,
    order_type="quantity",
    stop_loss_pct=3.0,
    profit_take_ratio=0.6,
    **strategy_params
):
    """
    运行MA交叉策略（带移动止损）的多品种组合回测

    参数:
    dfs_dict: 字典，键为品种名称，值为对应的DataFrame
    symbol_params: 字典，键为品种名称，值为该品种的参数字典，用于为不同品种设置不同参数
                  例如: {"BTCUSDT": {"position_size": 0.5, "stop_loss_pct": 2.0},
                        "ETHUSDT": {"position_size": 5, "stop_loss_pct": 3.0}}
    fast_window: 快速均线窗口，默认为10
    slow_window: 慢速均线窗口，默认为20
    position_size: 每次开仓的数量、金额或资金比例，默认为10
    rate: 手续费率，默认为0.0003
    slippage: 滑点，默认为0.001
    capital: 初始资金，默认为50000
    plot_show: 是否在浏览器中显示图表，默认为True
    plot_save: 是否保存图表文件，默认为True
    order_type: 下单方式，可选"quantity"(按币数量)、"amount"(按金额)或"ratio"(按资金比例)，默认为"quantity"
    stop_loss_pct: 止损百分比，默认为3.0%
    profit_take_ratio: 回撤比例，当利润回撤到最高价和止盈线之间指定比例时止盈，默认为0.6(60%)
    **strategy_params: 其他策略参数
    """
    # 创建组合回测引擎
    portfolio_engine = PortfolioEngine()

    # 设置组合回测参数
    # 找出所有DataFrame中最早的开始日期和最晚的结束日期
    start_dates = [df.index[0] for df in dfs_dict.values()]
    end_dates = [df.index[-1] for df in dfs_dict.values()]

    start_date = min(start_dates)
    end_date = max(end_dates)

    # 设置组合回测参数
    portfolio_engine.set_parameters(
        start=start_date,
        end=end_date,
        capital=capital,
        annual_days=365  # 加密货币市场全年交易
    )

    # 为每个品种创建回测引擎
    for symbol, df in dfs_dict.items():
        print(f"准备 {symbol} 的回测数据...")

        # 检查必要的列是否存在
        required_columns = ["open", "high", "low", "close", "volume"]
        missing_columns = [
            col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: {symbol} 数据中缺少必要的列: {missing_columns}")
            continue

        # 检查数据类型
        try:
            # 检查价格和成交量列是否可以转换为float类型
            for col in ["open", "high", "low", "close", "volume"]:
                df[col] = df[col].astype(float)
        except Exception as e:
            print(f"错误: {symbol} 数据类型转换失败: {e}")
            continue

        # 检查索引是否为datetime类型
        if not isinstance(df.index, pd.DatetimeIndex):
            print(f"警告: {symbol} 索引不是datetime类型，尝试转换")
            if "datetime" in df.columns:
                try:
                    df.index = pd.to_datetime(df["datetime"])
                except Exception as e:
                    print(f"错误: 无法将'datetime'列转换为日期索引: {e}")
                    continue
            else:
                print(f"错误: {symbol} 没有datetime列且索引不是datetime类型")
                continue

        # 创建Bar对象列表
        bars = []
        for row in df.itertuples():
            dt = row.Index
            bar = BarData(
                symbol=symbol,
                exchange=Exchange.BINANCE,
                datetime=dt,
                open_price=row.open,
                high_price=row.high,
                low_price=row.low,
                close_price=row.close,
                volume=row.volume,
                turnover=getattr(row, "turnover", row.volume * row.close),
                gateway_name="BACKTEST"
            )
            # 添加剩余K线数量属性，用于判断是否接近回测结束
            bar.remaining_bars = len(df) - df.index.get_loc(dt) - 1
            bars.append(bar)

        # 创建回测引擎
        engine = BacktestingEngine()

        # 设置回测参数
        params = {
            "vt_symbol": f"{symbol}.BINANCE",
            "start": df.index[0],
            "end": df.index[-1],
            "rate": rate,
            "slippage": slippage,
            "capital": capital / len(dfs_dict),  # 平均分配资金
            "fast_window": fast_window,
            "slow_window": slow_window,
            "position_size": position_size,
            "order_type": order_type,
            "stop_loss_pct": stop_loss_pct,
            "profit_take_ratio": profit_take_ratio
        }

        # 如果有针对该品种的特定参数，则使用这些参数覆盖默认参数
        if symbol_params and symbol in symbol_params:
            symbol_specific_params = symbol_params[symbol]
            params.update(symbol_specific_params)
            print(f"为 {symbol} 设置特定参数: {symbol_specific_params}")

        # 添加其他策略参数
        if strategy_params:
            params.update(strategy_params)

        # 设置参数
        engine.set_parameters(**params)

        # 添加策略
        engine.add_strategy(MACrossTrailingStopStrategy)

        # 加载数据
        engine.history_data = bars

        # 将引擎添加到组合回测引擎
        portfolio_engine.add_engine(symbol, engine)

    # 运行组合回测
    print("开始组合回测...")
    portfolio_engine.run_backtesting()

    # 计算组合回测结果
    portfolio_engine.calculate_result()

    # 计算统计指标
    stats = portfolio_engine.calculate_statistics()

    # 显示统计指标 - 组合策略使用日级别聚合计算
    print("\n====== 组合回测结果 (日级别聚合) ======")
    print(f"策略: MACrossTrailingStopStrategy (多品种)")
    print(f"开始日期: {stats['start_date']}  结束日期: {stats['end_date']}")
    print(
        f"总交易日: {stats['total_days']}  盈利日: {stats['profit_days']}  亏损日: {stats['loss_days']}")
    print(f"初始资金: {stats['capital']:,.2f}  结束资金: {stats['end_balance']:,.2f}")
    print(f"总收益率: {stats['total_return']:,.2f}%")
    print(
        f"最大回撤: {stats['max_ddpercent']:,.2f}%  夏普比率: {stats['sharpe_ratio']:,.2f}")
    print("注意: 单个品种使用逐K线计算，组合结果使用日级别聚合")

    # 创建可交互式图表
    if plot_show or plot_save:
        try:
            # 使用PlotlyChartEngine创建可交互式图表
            chart_engine = PlotlyChartEngine()

            # 确定图表标题中的仓位描述
            if order_type == "quantity":
                position_desc = f"每次开仓数量: {position_size}"
            elif order_type == "amount":
                position_desc = f"每次开仓金额: {position_size}"
            elif order_type == "ratio":
                position_desc = f"每次开仓资金比例: {position_size * 100:.2f}%"
            else:
                position_desc = f"每次开仓数量: {position_size}"

            # 创建组合回测的图表标题
            symbols_str = "_".join(dfs_dict.keys())
            symbols_display = ", ".join(dfs_dict.keys())

            # 如果使用了symbol_params，则在标题中显示"多参数"
            if symbol_params:
                portfolio_title = f"MA交叉策略(带移动止损) - 多品种多参数组合回测 [{symbols_display}]"
            else:
                portfolio_title = f"MA交叉策略(带移动止损) - 多品种组合回测 [{symbols_display}] (MA{fast_window}/{slow_window}, {position_desc})"

            # 为每个单独的品种创建图表（如果需要）
            if plot_save:
                for symbol, engine in portfolio_engine.engines.items():
                    # 获取该品种的特定参数
                    symbol_fast_window = fast_window
                    symbol_slow_window = slow_window
                    symbol_stop_loss_pct = stop_loss_pct
                    symbol_profit_take_ratio = profit_take_ratio
                    symbol_position_size = position_size

                    if symbol_params and symbol in symbol_params:
                        if "fast_window" in symbol_params[symbol]:
                            symbol_fast_window = symbol_params[symbol]["fast_window"]
                        if "slow_window" in symbol_params[symbol]:
                            symbol_slow_window = symbol_params[symbol]["slow_window"]
                        if "stop_loss_pct" in symbol_params[symbol]:
                            symbol_stop_loss_pct = symbol_params[symbol]["stop_loss_pct"]
                        if "profit_take_ratio" in symbol_params[symbol]:
                            symbol_profit_take_ratio = symbol_params[symbol]["profit_take_ratio"]
                        if "position_size" in symbol_params[symbol]:
                            symbol_position_size = symbol_params[symbol]["position_size"]

                    # 创建该品种的图表路径和标题
                    # 使用日期时间作为文件名的一部分，避免覆盖
                    import datetime
                    now_str = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    symbol_chart_path = f"vnpy_backtester/charts/{now_str}_ma_cross_trailing_stop_{symbol}.html"

                    if order_type == "quantity":
                        symbol_position_desc = f"每次开仓数量: {symbol_position_size}"
                    elif order_type == "amount":
                        symbol_position_desc = f"每次开仓金额: {symbol_position_size}"
                    elif order_type == "ratio":
                        symbol_position_desc = f"每次开仓资金比例: {symbol_position_size * 100:.2f}%"
                    else:
                        symbol_position_desc = f"每次开仓数量: {symbol_position_size}"

                    symbol_title = f"{symbol} - MA交叉策略(带移动止损) | MA{symbol_fast_window}/MA{symbol_slow_window} | {symbol_position_desc} | 止损: {symbol_stop_loss_pct}% | 回撤止盈: {symbol_profit_take_ratio*100:.1f}%"

                    # 创建并保存该品种的图表
                    chart_engine.create_chart(
                        engine=engine,
                        title=symbol_title,
                        save_path=symbol_chart_path,
                        show=False,  # 不显示单个品种的图表
                        initial_capital=capital / len(dfs_dict)
                    )
                    print(f"{symbol} 图表已保存为 {symbol_chart_path}")

            # 创建并保存/显示组合回测的图表
            # 注意：这里我们需要为portfolio_engine创建一个可交互式的图表
            # 由于portfolio_engine.show_chart()使用的是matplotlib，我们需要创建一个新的方法

            # 创建组合回测的图表文件路径
            import datetime
            now_str = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            portfolio_chart_path = f"vnpy_backtester/charts/{now_str}_portfolio_{symbols_str}.html"

            # 准备组合回测的数据
            if portfolio_engine.daily_df is None:
                portfolio_engine.calculate_result()

            # 创建一个新的图表对象
            portfolio_fig = make_portfolio_plotly_chart(
                portfolio_engine=portfolio_engine,
                title=portfolio_title,
                save_path=portfolio_chart_path if plot_save else None,
                show=plot_show,
                initial_capital=capital
            )

            if plot_save and portfolio_fig:
                print(f"组合回测图表已保存为 {portfolio_chart_path}")

        except Exception as e:
            print(f"图表处理时出错: {e}")
            import traceback
            traceback.print_exc()

            # 如果Plotly图表创建失败，尝试使用原始的matplotlib图表
            if plot_show:
                try:
                    portfolio_engine.show_chart()
                except Exception as e2:
                    print(f"原始图表显示也出错: {e2}")

# 添加一个函数来为portfolio_engine创建Plotly图表


def make_portfolio_plotly_chart(portfolio_engine, title="组合回测结果", save_path=None, show=True, initial_capital=None):
    """
    为portfolio_engine创建Plotly图表

    参数:
    portfolio_engine: PortfolioEngine实例
    title: 图表标题
    save_path: 保存路径，如果为None则不保存
    show: 是否显示图表
    initial_capital: 初始资金（可选，仅用于显示在标题中）

    返回:
    fig: Plotly图表对象
    """
    try:
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots
        import traceback

        # 确保daily_df已经计算
        if portfolio_engine.daily_df is None:
            portfolio_engine.calculate_result()

        # 获取数据
        df = portfolio_engine.daily_df

        # 创建图表
        fig = make_subplots(
            rows=3,
            cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=("资金曲线", "每日盈亏", "回撤"),
            row_heights=[0.5, 0.25, 0.25]
        )

        # 添加资金曲线
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df["balance"],
                mode="lines",
                name="资金曲线",
                line=dict(color="blue", width=2)
            ),
            row=1, col=1
        )

        # 添加每日盈亏
        fig.add_trace(
            go.Bar(
                x=df.index,
                y=df["net_pnl"],
                name="每日盈亏",
                marker_color=["green" if pnl >=
                              0 else "red" for pnl in df["net_pnl"]]
            ),
            row=2, col=1
        )

        # 添加回撤
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df["drawdown_percent"],
                mode="lines",
                name="回撤",
                line=dict(color="red", width=2)
            ),
            row=3, col=1
        )

        # 更新布局
        # 如果提供了初始资金，在标题中显示
        if initial_capital:
            display_title = f"{title} (初始资金: {initial_capital:,.0f})"
        else:
            display_title = title

        # 获取最终资金和收益率
        final_balance = df["balance"].iloc[-1] if not df.empty else 0
        total_return = ((final_balance / portfolio_engine.capital) -
                        1) * 100 if portfolio_engine.capital > 0 else 0

        # 添加收益率信息到标题
        display_title = f"{display_title}<br><span style='font-size:0.8em'>最终资金: {final_balance:,.0f} | 总收益率: {total_return:.2f}%</span>"

        fig.update_layout(
            title={
                'text': display_title,
                'y': 0.95,
                'x': 0.5,
                'xanchor': 'center',
                'yanchor': 'top',
                'font': {'size': 20}
            },
            autosize=True,  # 自动调整大小
            showlegend=False,
            template="plotly_white",
            margin=dict(t=120, b=50, l=50, r=50)  # 调整边距，为标题留出空间
        )

        # 显示图表
        if show:
            # 使用浏览器渲染器，并设置配置选项
            fig.show(
                renderer="browser",
                config={
                    'responsive': True,  # 响应式布局
                    'scrollZoom': True,  # 允许滚轮缩放
                    'displayModeBar': True,  # 显示模式栏
                    'displaylogo': False,  # 不显示Plotly logo
                }
            )

        # 保存图表
        if save_path:
            # 确保目录存在
            import os
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)

            # 添加响应式布局配置
            fig.write_html(
                save_path,
                config={
                    'responsive': True,  # 响应式布局
                    'scrollZoom': True,  # 允许滚轮缩放
                    'displayModeBar': True,  # 显示模式栏
                    'displaylogo': False,  # 不显示Plotly logo
                },
                full_html=True,  # 生成完整的HTML文件
                include_plotlyjs=True,  # 包含Plotly.js库
                auto_open=False  # 不自动打开文件
            )

        return fig
    except Exception as e:
        print(f"创建组合回测图表时出错: {e}")
        traceback.print_exc()
        return None


# 创建数据服务实例
ds = DataService()

# 获取多个品种的数据
dfs_dict = {
    "ETHUSDT": ds['ETHUSDT_15m_2020_2025']['2021-10-1':],
    "BTCUSDT": ds['BTCUSDT_15m_2020_2025']['2021-10-1':],
    # "SOLUSDT": ds['SOLUSDT_15m_2020_2025']['2021-10-1':],
    "BNBUSDT": ds['BNBUSDT_15m_2020_2025']['2021-10-1':],
    # "LTCUSDT": ds['LTCUSDT_15m_2020_2025']['2021-10-1':],
    # 可以添加更多品种
}

# 为不同品种设置不同参数
symbol_params = {
    "ETHUSDT": {
        "position_size": 20,
        "fast_window": 5,
        "slow_window": 100,
        "stop_loss_pct": 2.0,
        "profit_take_ratio": 0.8
    },
    "BTCUSDT": {
        "position_size": 1,
        "fast_window": 5,
        "slow_window": 100,
        "stop_loss_pct": 2.0,
        "profit_take_ratio": 0.8
    },
    # "SOLUSDT": {
    #     "position_size": 300,
    #     "fast_window": 5,
    #     "slow_window": 100,
    #     "stop_loss_pct": 2.0,
    #     "profit_take_ratio": 0.8
    # },
    "BNBUSDT": {
        "position_size": 100,
        "fast_window": 5,
        "slow_window": 100,
        "stop_loss_pct": 2.0,
        "profit_take_ratio": 0.8
    },
    # "LTCUSDT": {
    #     "position_size": 20,
    #     "fast_window": 5,
    #     "slow_window": 100,
    #     "stop_loss_pct": 2.0,
    #     "profit_take_ratio": 0.8
    # },
}

# 运行组合回测
portfolio_engine = run_ma_cross_trailing_stop_portfolio_backtest(
    dfs_dict=dfs_dict,
    symbol_params=symbol_params,  # 传入品种特定参数
    # 以下是默认参数，会被symbol_params中的特定参数覆盖
    fast_window=5,
    slow_window=100,
    position_size=1,
    rate=0.0003,
    slippage=0.01,
    capital=300000,  # 总资金，会平均分配给每个品种
    order_type="quantity",
    stop_loss_pct=2.0,
    profit_take_ratio=0.3,
    plot_show=True,
    plot_save=True,
)
