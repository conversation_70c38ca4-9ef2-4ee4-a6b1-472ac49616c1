"""
图表引擎 - 使用Plotly绘制回测结果图表
"""

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from datetime import datetime
import traceback


class PlotlyChartEngine:
    """
    Plotly图表引擎 - 用于绘制回测结果图表
    """

    def __init__(self):
        """初始化图表引擎"""
        self.fig = None

    def create_chart_by_bar(self, engine, title="回测结果", save_path=None, show=True, initial_capital=None):
        """
        基于每根K线创建回测结果图表 - 更准确地反映多仓位策略的资金变化

        参数:
        engine: BacktestingEngine实例
        title: 图表标题
        save_path: 保存路径，如果为None则不保存
        show: 是否显示图表
        initial_capital: 初始资金，如果为None则尝试从engine获取

        返回:
        fig: Plotly图表对象
        """
        # 获取初始资金
        if initial_capital is None:
            if hasattr(engine, 'capital'):
                initial_capital = engine.capital
            elif hasattr(engine, 'stats') and 'capital' in engine.stats:
                initial_capital = engine.stats['capital']
            else:
                # 从统计结果中获取
                stats = engine.calculate_statistics()
                initial_capital = stats.get('capital', 100000)

        try:
            # 获取所有交易记录
            trades = list(engine.trades.values())
            if not trades:
                print("没有交易记录，无法创建图表")
                return None

            # 按时间排序交易
            trades.sort(key=lambda x: x.datetime)

            # 获取K线数据的时间范围
            if hasattr(engine, 'history_data') and engine.history_data:
                bar_times = [bar.datetime for bar in engine.history_data]
                bar_times.sort()
            else:
                # 如果没有K线数据，使用交易时间范围
                bar_times = [trade.datetime for trade in trades]
                bar_times = sorted(list(set(bar_times)))

            # 计算每根K线的资金变化
            balance_data = []
            current_balance = initial_capital
            trade_index = 0

            # 创建开仓交易的字典，用于匹配平仓交易
            # key: (direction, price, volume), value: trade
            open_positions = {}

            for bar_time in bar_times:
                # 处理当前K线时间的所有交易
                bar_pnl = 0.0
                bar_commission = 0.0
                bar_slippage = 0.0

                while trade_index < len(trades) and trades[trade_index].datetime <= bar_time:
                    trade = trades[trade_index]

                    # 计算手续费和滑点
                    volume = trade.volume
                    price = trade.price
                    size = getattr(engine, 'size', 1.0)
                    rate = getattr(engine, 'rate', 0.0003)
                    slippage_rate = getattr(engine, 'slippage', 0.0)

                    commission = volume * size * price * rate
                    slippage = volume * size * slippage_rate
                    bar_commission += commission
                    bar_slippage += slippage

                    if trade.offset.value == "开":  # 开仓交易
                        # 记录开仓交易，等待匹配平仓
                        key = f"{trade.direction.value}_{trade.price:.6f}_{trade.volume:.6f}"
                        open_positions[key] = trade

                    elif trade.offset.value == "平":  # 平仓交易
                        # 寻找对应的开仓交易计算盈亏
                        # 简化匹配：寻找相同数量的相反方向交易
                        matched_open = None

                        # 寻找匹配的开仓交易 - 改进匹配算法
                        # 优先匹配相同数量的交易，如果没有则匹配最接近的
                        best_match = None
                        best_key = None
                        min_volume_diff = float('inf')

                        for key, open_trade in open_positions.items():
                            # 检查方向是否匹配（平仓方向与开仓方向相反）
                            direction_match = ((trade.direction.value == "多" and open_trade.direction.value == "空") or
                                               (trade.direction.value == "空" and open_trade.direction.value == "多"))

                            if direction_match:
                                volume_diff = abs(
                                    open_trade.volume - trade.volume)
                                if volume_diff < min_volume_diff:
                                    min_volume_diff = volume_diff
                                    best_match = open_trade
                                    best_key = key

                                    # 如果找到完全匹配的，直接使用
                                    if volume_diff < 0.0001:
                                        break

                        if best_match:
                            matched_open = best_match
                            del open_positions[best_key]

                        if matched_open:
                            # 计算盈亏
                            if trade.direction.value == "多":  # 买入平仓（平空仓）
                                # 空仓盈亏 = (开仓价 - 平仓价) * 数量 * 合约乘数
                                pnl = (matched_open.price -
                                       trade.price) * trade.volume * size
                            else:  # 卖出平仓（平多仓）
                                # 多仓盈亏 = (平仓价 - 开仓价) * 数量 * 合约乘数
                                pnl = (trade.price - matched_open.price) * \
                                    trade.volume * size

                            bar_pnl += pnl

                    trade_index += 1

                # 计算净盈亏（扣除手续费和滑点）
                net_bar_pnl = bar_pnl - bar_commission - bar_slippage

                # 更新余额
                current_balance += net_bar_pnl
                balance_data.append({
                    'datetime': bar_time,
                    'balance': current_balance,
                    'pnl': net_bar_pnl,
                    'gross_pnl': bar_pnl,
                    'commission': bar_commission,
                    'slippage': bar_slippage
                })

            # 如果没有足够的数据点
            if len(balance_data) <= 1:
                print("数据点太少，无法创建有意义的图表")
                return None

            # 提取数据用于绘图
            datetimes = [item['datetime'] for item in balance_data]
            balances = [item['balance'] for item in balance_data]
            pnls = [item['pnl'] for item in balance_data]

            # 计算收益率
            returns = []
            for i in range(len(balances)):
                if i == 0 or balances[i-1] == 0:
                    returns.append(0)
                else:
                    bar_return = (
                        balances[i] - balances[i-1]) / balances[i-1] * 100
                    returns.append(bar_return)

            # 计算回撤
            drawdown = []
            max_balance = initial_capital
            for balance in balances:
                max_balance = max(max_balance, balance)
                drawdown_val = (max_balance - balance) / \
                    max_balance * 100 if max_balance > 0 else 0
                drawdown.append(drawdown_val)

            # 转换时间格式
            time_strings = [dt.strftime(
                '%Y-%m-%d %H:%M') if hasattr(dt, 'strftime') else str(dt) for dt in datetimes]

            # 创建图表
            self.fig = make_subplots(
                rows=3,
                cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                subplot_titles=("资金曲线 (按K线)", "收益率 (按K线)", "回撤 (按K线)"),
                row_heights=[0.5, 0.25, 0.25]
            )

            # 添加资金曲线
            self.fig.add_trace(
                go.Scatter(
                    x=time_strings,
                    y=balances,
                    mode="lines",
                    name="资金曲线",
                    line=dict(color="blue", width=2)
                ),
                row=1, col=1
            )

            # 添加收益率（只显示非零的收益率）
            non_zero_returns = [(time_strings[i], returns[i])
                                for i in range(len(returns)) if abs(returns[i]) > 0.001]
            if non_zero_returns:
                return_times, return_values = zip(*non_zero_returns)
                self.fig.add_trace(
                    go.Bar(
                        x=return_times,
                        y=return_values,
                        name="K线收益率",
                        marker_color=["green" if r >=
                                      0 else "red" for r in return_values]
                    ),
                    row=2, col=1
                )

            # 添加回撤
            self.fig.add_trace(
                go.Scatter(
                    x=time_strings,
                    y=drawdown,
                    mode="lines",
                    name="回撤",
                    line=dict(color="red", width=2)
                ),
                row=3, col=1
            )

            # 更新布局
            self.fig.update_layout(
                title=f"{title} (基于K线级别计算)",
                autosize=True,
                showlegend=False,
                template="plotly_white"
            )

            # 显示图表
            if show:
                self.fig.show(renderer="browser")

            # 保存图表
            if save_path:
                import os
                save_dir = os.path.dirname(save_path)
                if save_dir and not os.path.exists(save_dir):
                    os.makedirs(save_dir, exist_ok=True)

                self.fig.write_html(
                    save_path,
                    config={
                        'responsive': True,
                        'scrollZoom': True,
                        'displayModeBar': True,
                        'displaylogo': False,
                    }
                )

            return self.fig

        except Exception as e:
            print(f"创建K线级别图表时出错: {e}")
            traceback.print_exc()
            return None

    def create_chart(self, engine, title="回测结果", save_path=None, show=True, initial_capital=None):
        """
        创建回测结果图表 - 默认使用逐K线计算，提供更精确的资金曲线

        参数:
        engine: BacktestingEngine实例
        title: 图表标题
        save_path: 保存路径，如果为None则不保存
        show: 是否显示图表
        initial_capital: 初始资金，如果为None则尝试从engine获取

        返回:
        fig: Plotly图表对象
        """
        # 强制使用K线级别的图表创建方法，确保逐K线计算
        return self.create_chart_by_bar(
            engine=engine,
            title=f"{title} (逐K线计算)",
            save_path=save_path,
            show=show,
            initial_capital=initial_capital
        )

    def save_chart(self, path):
        """
        保存图表

        参数:
        path: 保存路径
        """
        if self.fig:
            try:
                # 确保目录存在
                import os
                save_dir = os.path.dirname(path)
                if save_dir and not os.path.exists(save_dir):
                    os.makedirs(save_dir, exist_ok=True)

                # 添加响应式布局配置
                self.fig.write_html(
                    path,
                    config={
                        'responsive': True,  # 响应式布局
                        'scrollZoom': True,  # 允许滚轮缩放
                        'displayModeBar': True,  # 显示模式栏
                        'displaylogo': False,  # 不显示Plotly logo
                    }
                )
                print(f"图表已保存为 {path}")
                return True
            except Exception as e:
                print(f"保存图表时出错: {e}")
                traceback.print_exc()
                return False
        else:
            print("没有图表可保存")
            return False

    def show_chart(self):
        """显示图表"""
        if self.fig:
            self.fig.show(renderer="browser")  # 使用浏览器渲染器
        else:
            print("没有图表可显示")
